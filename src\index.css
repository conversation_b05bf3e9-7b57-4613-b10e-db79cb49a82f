
@import url('https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&family=Nunito:wght@300;400;500;600;700&family=Comic+Neue:wght@300;400;700&display=swap');

/* Luxury Premium Fonts */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=<PERSON>oka+One&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --radius: 1rem;
    
    --background: 195 81% 99%;
    --foreground: 200 50% 15%;

    --card: 0 0% 100%;
    --card-foreground: 200 50% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 200 50% 15%;

    --primary: 195 95% 45%;
    --primary-foreground: 0 0% 100%;

    --secondary: 142 80% 45%;
    --secondary-foreground: 0 0% 100%;

    --muted: 210 20% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 42 94% 50%;
    --accent-foreground: 0 0% 0%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 195 95% 45%;

    --sidebar-background: 195 81% 99%;
    --sidebar-foreground: 200 50% 15%;
    --sidebar-primary: 195 95% 45%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 195 95% 45%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Luxury Design Tokens */
    --luxury-gold: #FFD700;
    --luxury-gold-dark: #B8860B;
    --luxury-gold-light: #FFF8DC;
    --luxury-platinum: #E5E4E2;
    --luxury-platinum-dark: #C0C0C0;
    --luxury-platinum-light: #F8F8FF;
    --luxury-purple: #663399;
    --luxury-purple-dark: #4B0082;
    --luxury-purple-light: #E6E6FA;
    --luxury-emerald: #50C878;
    --luxury-emerald-dark: #228B22;
    --luxury-emerald-light: #F0FFF0;
    --luxury-rose-gold: #E8B4B8;
    --luxury-rose-gold-dark: #B76E79;
    --luxury-rose-gold-light: #FFF0F5;
    --luxury-navy: #1B1B3A;
    --luxury-navy-dark: #0F0F23;
    --luxury-navy-light: #F0F0FF;

    /* Playful Accent Colors */
    --playful-pink: #FF69B4;
    --playful-blue: #00BFFF;
    --playful-yellow: #FFD700;
    --playful-coral: #FF7F50;
    --playful-mint: #98FB98;

    /* Luxury Spacing */
    --luxury-spacing-xs: 0.25rem;
    --luxury-spacing-sm: 0.5rem;
    --luxury-spacing-md: 1rem;
    --luxury-spacing-lg: 1.5rem;
    --luxury-spacing-xl: 2rem;
    --luxury-spacing-2xl: 3rem;
    --luxury-spacing-3xl: 4rem;

    /* Luxury Typography Scale */
    --luxury-text-xs: 0.75rem;
    --luxury-text-sm: 0.875rem;
    --luxury-text-base: 1rem;
    --luxury-text-lg: 1.125rem;
    --luxury-text-xl: 1.25rem;
    --luxury-text-2xl: 1.5rem;
    --luxury-text-3xl: 1.875rem;
    --luxury-text-4xl: 2.25rem;
    --luxury-text-5xl: 3rem;
    --luxury-text-6xl: 3.75rem;
    --luxury-text-7xl: 4.5rem;
    --luxury-text-8xl: 6rem;
    --luxury-text-9xl: 8rem;

    /* Luxury Animation Durations */
    --luxury-duration-fast: 0.15s;
    --luxury-duration-normal: 0.3s;
    --luxury-duration-slow: 0.6s;
    --luxury-duration-slower: 1s;

    /* Luxury Shadows */
    --luxury-shadow-sm: 0 1px 2px 0 rgba(255, 215, 0, 0.05);
    --luxury-shadow: 0 1px 3px 0 rgba(255, 215, 0, 0.1), 0 1px 2px 0 rgba(255, 215, 0, 0.06);
    --luxury-shadow-md: 0 4px 6px -1px rgba(255, 215, 0, 0.1), 0 2px 4px -1px rgba(255, 215, 0, 0.06);
    --luxury-shadow-lg: 0 10px 15px -3px rgba(255, 215, 0, 0.1), 0 4px 6px -2px rgba(255, 215, 0, 0.05);
    --luxury-shadow-xl: 0 20px 25px -5px rgba(255, 215, 0, 0.1), 0 10px 10px -5px rgba(255, 215, 0, 0.04);
    --luxury-shadow-2xl: 0 25px 50px -12px rgba(255, 215, 0, 0.25);
    --luxury-shadow-inner: inset 0 2px 4px 0 rgba(255, 215, 0, 0.06);

    /* Glass Morphism */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-backdrop: blur(10px);
  }

  .dark {
    --background: 198 44% 12%;
    --foreground: 195 10% 95%;

    --card: 198 44% 14%;
    --card-foreground: 195 10% 95%;

    --popover: 198 44% 14%;
    --popover-foreground: 195 10% 95%;

    --primary: 195 95% 45%;
    --primary-foreground: 0 0% 100%;

    --secondary: 142 80% 45%;
    --secondary-foreground: 0 0% 100%;

    --muted: 198 44% 18%;
    --muted-foreground: 215 20% 75%;

    --accent: 42 94% 50%;
    --accent-foreground: 0 0% 0%;

    --destructive: 0 62.8% 50.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 198 44% 18%;
    --input: 198 44% 18%;
    --ring: 195 95% 45%;
    
    --sidebar-background: 198 44% 16%;
    --sidebar-foreground: 195 10% 95%;
    --sidebar-primary: 195 95% 55%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 198 44% 21%;
    --sidebar-accent-foreground: 195 95% 55%;
    --sidebar-border: 198 44% 21%;
    --sidebar-ring: 195 95% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-nunito;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-quicksand font-bold;
  }
}

@layer components {
  .card-hover {
    @apply transition-all duration-300 hover:shadow-card hover:-translate-y-1;
  }
  
  .button-hover {
    @apply transition-all duration-300 hover:shadow-button hover:-translate-y-0.5;
  }

  .illustration-container {
    @apply relative overflow-hidden rounded-xl;
  }

  /* Luxury Components */
  .luxury-card {
    @apply bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-6 shadow-luxury;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    backdrop-filter: var(--glass-backdrop);
  }

  .luxury-button {
    @apply relative overflow-hidden rounded-full px-8 py-4 font-montserrat font-semibold text-white transition-all duration-300;
    background: linear-gradient(135deg, var(--luxury-gold) 0%, #FFA500 50%, #FF8C00 100%);
    box-shadow: var(--luxury-shadow-lg);
  }

  .luxury-button:hover {
    @apply scale-105 shadow-glow-gold;
    transform: translateY(-2px) scale(1.05);
  }

  .luxury-button::before {
    content: '';
    @apply absolute inset-0 bg-shimmer opacity-0 transition-opacity duration-300;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    background-size: 200% 100%;
  }

  .luxury-button:hover::before {
    @apply opacity-100;
    animation: shimmer 1.5s ease-in-out;
  }

  .luxury-text-gradient {
    background: linear-gradient(135deg, var(--luxury-gold) 0%, var(--luxury-purple) 50%, var(--luxury-emerald) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .luxury-gold-gradient {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF8C00 100%);
  }

  .luxury-purple-gradient {
    background: linear-gradient(135deg, #663399 0%, #4B0082 50%, #8A2BE2 100%);
  }

  .luxury-emerald-gradient {
    background: linear-gradient(135deg, #50C878 0%, #228B22 50%, #32CD32 100%);
  }

  .luxury-rose-gold-gradient {
    background: linear-gradient(135deg, #E8B4B8 0%, #B76E79 50%, #CD919E 100%);
  }

  .luxury-rainbow-gradient {
    background: linear-gradient(135deg, #FFD700 0%, #FF69B4 25%, #00BFFF 50%, #50C878 75%, #E8B4B8 100%);
  }

  .glass-morphism {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .luxury-glow {
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
  }

  .luxury-glow:hover {
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
  }

  .sparkle-effect {
    position: relative;
  }

  .sparkle-effect::before,
  .sparkle-effect::after {
    content: '✨';
    position: absolute;
    font-size: 1rem;
    animation: sparkle 2s ease-in-out infinite;
    pointer-events: none;
  }

  .sparkle-effect::before {
    top: -10px;
    right: -10px;
    animation-delay: 0s;
  }

  .sparkle-effect::after {
    bottom: -10px;
    left: -10px;
    animation-delay: 1s;
  }

  .floating-element {
    animation: luxury-float 8s ease-in-out infinite;
  }

  .fade-in-up {
    animation: fade-in-up 0.6s ease-out;
  }

  .slide-in-right {
    animation: slide-in-right 0.6s ease-out;
  }

  .scale-in {
    animation: scale-in 0.4s ease-out;
  }

  /* VIP Indicators */
  .vip-badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-montserrat font-bold text-white;
    background: linear-gradient(135deg, var(--luxury-gold) 0%, var(--luxury-purple) 100%);
    box-shadow: var(--luxury-shadow-md);
  }

  .premium-badge {
    @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-montserrat font-semibold;
    background: linear-gradient(135deg, var(--luxury-platinum) 0%, var(--luxury-gold) 100%);
    color: var(--luxury-navy);
  }

  .crown-icon {
    @apply inline-block w-4 h-4 mr-1;
    filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.5));
  }

  /* Luxury Typography */
  .luxury-heading {
    @apply font-playfair font-bold;
    background: linear-gradient(135deg, var(--luxury-gold) 0%, var(--luxury-purple) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(255, 215, 0, 0.1);
  }

  .luxury-subheading {
    @apply font-montserrat font-semibold text-luxury-navy;
  }

  .luxury-body {
    @apply font-inter text-gray-700 leading-relaxed;
  }

  .playful-text {
    @apply font-fredoka text-playful-pink;
  }

  /* Responsive Luxury Spacing */
  .luxury-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .luxury-section {
    @apply py-16 lg:py-24;
  }

  /* Advanced Luxury Animations */
  @keyframes luxury-sparkle {
    0%, 100% { opacity: 0; transform: scale(0) rotate(0deg); }
    50% { opacity: 1; transform: scale(1) rotate(180deg); }
  }

  @keyframes luxury-glow-pulse {
    0%, 100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.3); }
    50% { box-shadow: 0 0 40px rgba(255, 215, 0, 0.6); }
  }

  @keyframes luxury-float-gentle {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(1deg); }
    66% { transform: translateY(-5px) rotate(-1deg); }
  }

  @keyframes luxury-shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  @keyframes luxury-fade-in-up {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes luxury-scale-in {
    from {
      opacity: 0;
      transform: scale(0.8);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes luxury-ripple {
    from {
      transform: scale(0);
      opacity: 1;
    }
    to {
      transform: scale(4);
      opacity: 0;
    }
  }

  @keyframes luxury-pulse-glow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
    }
    50% {
      box-shadow: 0 0 40px rgba(255, 215, 0, 0.6), 0 0 60px rgba(255, 215, 0, 0.3);
    }
  }

  @keyframes luxury-text-glow {
    0%, 100% {
      text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
    }
    50% {
      text-shadow: 0 0 20px rgba(255, 215, 0, 0.6), 0 0 30px rgba(255, 215, 0, 0.3);
    }
  }

  /* Luxury Animation Classes */
  .animate-luxury-sparkle {
    animation: luxury-sparkle 2s ease-in-out infinite;
  }

  .animate-luxury-glow {
    animation: luxury-glow-pulse 3s ease-in-out infinite;
  }

  .animate-luxury-float {
    animation: luxury-float-gentle 6s ease-in-out infinite;
  }

  .animate-luxury-shimmer {
    animation: luxury-shimmer 2s linear infinite;
  }

  .animate-luxury-fade-in {
    animation: luxury-fade-in-up 0.8s ease-out;
  }

  .animate-luxury-scale {
    animation: luxury-scale-in 0.6s ease-out;
  }

  /* Luxury Interactive States */
  .luxury-hover-lift {
    @apply transition-all duration-500 ease-out;
  }

  .luxury-hover-lift:hover {
    @apply transform -translate-y-2 shadow-luxury-gold;
  }

  .luxury-hover-glow {
    @apply transition-all duration-300 ease-out;
  }

  .luxury-hover-glow:hover {
    @apply shadow-glow-gold;
  }

  /* VIP Indicators */
  .vip-badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-bold;
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: #1B1B3A;
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
  }

  .premium-badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-bold;
    background: linear-gradient(135deg, #E8B4B8 0%, #B76E79 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(232, 180, 184, 0.3);
  }

  .elite-badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-bold;
    background: linear-gradient(135deg, #663399 0%, #4B0082 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(102, 51, 153, 0.3);
  }

  /* Luxury Page Transitions */
  .luxury-page-enter {
    opacity: 0;
    transform: translateY(20px);
  }

  .luxury-page-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  }

  .luxury-page-exit {
    opacity: 1;
    transform: translateY(0);
  }

  .luxury-page-exit-active {
    opacity: 0;
    transform: translateY(-20px);
    transition: opacity 0.4s ease-in, transform 0.4s ease-in;
  }

  /* Luxury Scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(27, 27, 58, 0.1);
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #FFD700 0%, #663399 100%);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #FFA500 0%, #4B0082 100%);
  }

  /* Luxury Selection */
  ::selection {
    background: rgba(255, 215, 0, 0.3);
    color: #1B1B3A;
  }

  ::-moz-selection {
    background: rgba(255, 215, 0, 0.3);
    color: #1B1B3A;
  }

  /* Luxury Focus States */
  .luxury-focus:focus {
    outline: 2px solid #FFD700;
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(255, 215, 0, 0.1);
  }

  /* Smooth Scroll */
  html {
    scroll-behavior: smooth;
  }

  /* Luxury Body Background */
  body {
    background: linear-gradient(135deg, #0F0F23 0%, #1B1B3A 100%);
    min-height: 100vh;
  }

  /* Responsive Luxury Utilities */
  @media (max-width: 768px) {
    .luxury-container {
      @apply px-4;
    }

    .luxury-section {
      @apply py-12;
    }

    .luxury-card {
      @apply p-4 rounded-xl;
    }

    .luxury-heading {
      font-size: clamp(1.5rem, 4vw, 2.5rem);
    }
  }

  @media (min-width: 769px) and (max-width: 1024px) {
    .luxury-container {
      @apply px-6;
    }

    .luxury-section {
      @apply py-16;
    }

    .luxury-card {
      @apply p-6 rounded-2xl;
    }
  }

  @media (min-width: 1025px) {
    .luxury-container {
      @apply px-8;
    }

    .luxury-section {
      @apply py-20 lg:py-24;
    }

    .luxury-card {
      @apply p-8 rounded-3xl;
    }
  }

  /* Touch-friendly interactions */
  @media (hover: none) and (pointer: coarse) {
    .luxury-hover-lift:hover {
      transform: none;
    }

    .luxury-hover-lift:active {
      transform: translateY(-4px) scale(0.98);
    }

    .luxury-hover-glow:hover {
      box-shadow: none;
    }

    .luxury-hover-glow:active {
      box-shadow: 0 0 20px rgba(255, 215, 0, 0.4);
    }
  }

  /* Reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .animate-luxury-sparkle,
    .animate-luxury-glow,
    .animate-luxury-float,
    .animate-luxury-shimmer,
    .animate-luxury-fade-in,
    .animate-luxury-scale {
      animation: none;
    }

    .luxury-hover-lift {
      transition: none;
    }
  }

  /* High contrast mode */
  @media (prefers-contrast: high) {
    .luxury-heading {
      background: linear-gradient(135deg, #FFFF00 0%, #FFFFFF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .luxury-card {
      border: 2px solid #FFFFFF;
      background: #000000;
    }

    .vip-badge,
    .premium-badge,
    .elite-badge {
      background: #FFFF00 !important;
      color: #000000 !important;
      border: 2px solid #FFFFFF;
    }
  }

  /* Screen reader only content */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Focus indicators */
  .luxury-focus-ring:focus {
    outline: 3px solid #FFD700;
    outline-offset: 2px;
  }

  @media (prefers-contrast: high) {
    .luxury-focus-ring:focus {
      outline: 3px solid #FFFF00;
      outline-offset: 2px;
    }
  }

  /* Large text preferences */
  @media (prefers-reduced-data: reduce) {
    .animate-luxury-sparkle,
    .animate-luxury-glow,
    .animate-luxury-float {
      animation: none;
    }
  }

  .luxury-grid {
    @apply grid gap-8 md:gap-12 lg:gap-16;
  }
}
